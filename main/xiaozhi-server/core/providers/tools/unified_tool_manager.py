"""统一工具管理器"""

from typing import Dict, List, Optional, Any
from config.logger import setup_logging
from plugins_func.register import Action, ActionResponse
from .base import ToolType, ToolDefinition, ToolExecutor


class ToolManager:
    """统一工具管理器，管理所有类型的工具"""

    def __init__(self, conn):
        self.conn = conn
        self.logger = setup_logging()
        self.executors: Dict[ToolType, ToolExecutor] = {}
        self._cached_tools: Optional[Dict[str, ToolDefinition]] = None
        self._cached_function_descriptions: Optional[List[Dict[str, Any]]] = None

    def register_executor(self, tool_type: ToolType, executor: ToolExecutor):
        """注册工具执行器"""
        self.executors[tool_type] = executor
        self._invalidate_cache()
        self.logger.info(f"注册工具执行器: {tool_type.value}")

    def _invalidate_cache(self):
        """使缓存失效"""
        self._cached_tools = None
        self._cached_function_descriptions = None

    def get_all_tools(self) -> Dict[str, ToolDefinition]:
        """获取所有工具定义，智能合并MCP接入点工具和检索工具，避免重复"""
        if self._cached_tools is not None:
            return self._cached_tools

        all_tools = {}
        mcp_endpoint_tools = {}
        other_tools = {}

        # 分类收集工具
        for tool_type, executor in self.executors.items():
            try:
                tools = executor.get_tools()
                if tool_type.name == 'MCP_ENDPOINT':
                    # MCP接入点工具（用户自己装的）
                    mcp_endpoint_tools.update(tools)
                    self.logger.debug(f"收集到 {len(tools)} 个MCP接入点工具: {list(tools.keys())}")
                else:
                    # 其他工具（包括检索到的默认工具）
                    other_tools.update(tools)
                    self.logger.debug(f"收集到 {len(tools)} 个{tool_type.value}工具: {list(tools.keys())}")
            except Exception as e:
                self.logger.error(f"获取{tool_type.value}工具时出错: {e}")

        # 优先使用MCP接入点工具，避免与检索工具重复
        all_tools.update(other_tools)  # 先添加其他工具

        # 添加MCP接入点工具，如果有重名则优先使用MCP接入点的版本
        for name, definition in mcp_endpoint_tools.items():
            if name in all_tools:
                self.logger.info(f"🔄 工具名称重复，优先使用MCP接入点版本: {name}")
            all_tools[name] = definition

        self.logger.info(f"🛠️ 工具合并完成: MCP接入点工具 {len(mcp_endpoint_tools)} 个, 其他工具 {len(other_tools)} 个, 总计 {len(all_tools)} 个")

        self._cached_tools = all_tools
        return all_tools

    def _create_dummy_parameters(self) -> Dict[str, Any]:
        """创建默认的参数结构"""
        return {
            'type': 'object',
            'properties': {
                'dummy': {
                    'type': 'string',
                    'description': '占位参数，无实际意义'
                }
            },
            'required': []
        }

    def _ensure_valid_parameters(self, parameters: Any, tool_name: str = 'unknown') -> Dict[str, Any]:
        """确保参数结构有效"""
        # 如果parameters不存在或不是dict，返回默认结构
        if not isinstance(parameters, dict):
            self.logger.debug(f"为工具 {tool_name} 兜底添加了完整的parameters结构")
            return self._create_dummy_parameters()

        # 确保基本字段存在
        if 'type' not in parameters:
            parameters['type'] = 'object'

        if not parameters.get('properties'):
            parameters['properties'] = {
                'dummy': {
                    'type': 'string',
                    'description': '占位参数，无实际意义'
                }
            }
            self.logger.debug(f"为工具 {tool_name} 兜底添加了dummy参数")

        if 'required' not in parameters:
            parameters['required'] = []

        # 修复JSON Schema中的数组类型缺少items的问题
        self._fix_array_schema_items(parameters, tool_name)

        return parameters

    def _fix_array_schema_items(self, schema: Dict[str, Any], tool_name: str, path: str = "") -> None:
        """递归修复JSON Schema中数组类型缺少items的问题"""
        if not isinstance(schema, dict):
            return

        for key, value in schema.items():
            current_path = f"{path}.{key}" if path else key

            if isinstance(value, dict):
                # 检查当前字段是否为数组类型且缺少items
                if value.get("type") == "array" and "items" not in value:
                    # 添加默认的items定义
                    value["items"] = {"type": "string"}
                    self.logger.debug(f"为工具 {tool_name} 的字段 {current_path} 添加了默认的items定义")

                # 处理复合类型 (oneOf, anyOf, allOf)
                for composite_key in ["oneOf", "anyOf", "allOf"]:
                    if composite_key in value and isinstance(value[composite_key], list):
                        for i, item in enumerate(value[composite_key]):
                            if isinstance(item, dict):
                                self._fix_array_schema_items(item, tool_name, f"{current_path}.{composite_key}[{i}]")

                # 递归处理嵌套对象
                self._fix_array_schema_items(value, tool_name, current_path)

            elif isinstance(value, list):
                # 处理数组中的对象
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        self._fix_array_schema_items(item, tool_name, f"{current_path}[{i}]")

    def _process_mcp_endpoint_tool(self, tool_definition: ToolDefinition) -> Dict[str, Any]:
        """处理MCP_ENDPOINT类型的工具"""
        desc = tool_definition.description
        if 'function' not in desc:
            return desc

        func = desc['function']
        # fullName赋值给name
        name = func.get('fullName') or func.get('name')
        description = func.get('description', '')
        parameters = self._ensure_valid_parameters(
            func.get('parameters', {}), name
        )

        # 只保留标准字段
        return {
            'type': 'function',
            'function': {
                'name': name,
                'description': description,
                'parameters': parameters
            }
        }

    def _process_standard_tool(self, tool_definition: ToolDefinition) -> Dict[str, Any]:
        """处理标准工具"""
        desc = tool_definition.description
        if 'function' in desc:
            func = desc['function']
            tool_name = func.get('name', 'unknown')
            # 确保参数结构完整
            func['parameters'] = self._ensure_valid_parameters(
                func.get('parameters'), tool_name
            )

        return desc

    def get_function_descriptions(self) -> List[Dict[str, Any]]:
        """获取所有工具的函数描述（OpenAI格式）"""
        if self._cached_function_descriptions is not None:
            return self._cached_function_descriptions

        descriptions = []
        tools = self.get_all_tools()
        
        for tool_definition in tools.values():
            if tool_definition.tool_type.name == 'MCP_ENDPOINT':
                desc = self._process_mcp_endpoint_tool(tool_definition)
            else:
                desc = self._process_standard_tool(tool_definition)
            
            descriptions.append(desc)

        self._cached_function_descriptions = descriptions
        return descriptions

    def has_tool(self, tool_name: str) -> bool:
        """检查是否存在指定工具"""
        tools = self.get_all_tools()
        return tool_name in tools

    def get_tool_type(self, tool_name: str) -> Optional[ToolType]:
        """获取工具类型"""
        tools = self.get_all_tools()
        tool_def = tools.get(tool_name)
        return tool_def.tool_type if tool_def else None

    async def execute_tool(
        self, tool_name: str, arguments: Dict[str, Any]
    ) -> ActionResponse:
        """执行工具调用"""
        try:
            # 查找工具类型
            tool_type = self.get_tool_type(tool_name)
            if not tool_type:
                return ActionResponse(
                    action=Action.NOTFOUND,
                    response=f"工具 {tool_name} 不存在",
                )

            # 获取对应的执行器
            executor = self.executors.get(tool_type)
            if not executor:
                return ActionResponse(
                    action=Action.ERROR,
                    response=f"工具类型 {tool_type.value} 的执行器未注册",
                )

            # 执行工具
            self.logger.info(f"执行工具: {tool_name}，参数: {arguments}")
            result = await executor.execute(self.conn, tool_name, arguments)
            self.logger.debug(f"工具执行结果: {result}")
            return result

        except Exception as e:
            self.logger.error(f"执行工具 {tool_name} 时出错: {e}")
            return ActionResponse(action=Action.ERROR, response=str(e))

    def get_supported_tool_names(self) -> List[str]:
        """获取所有支持的工具名称"""
        tools = self.get_all_tools()
        return list(tools.keys())

    def refresh_tools(self):
        """刷新工具缓存"""
        self._invalidate_cache()
        self.logger.info("工具缓存已刷新")

    def get_tool_statistics(self) -> Dict[str, int]:
        """获取工具统计信息"""
        stats = {}
        for tool_type, executor in self.executors.items():
            try:
                tools = executor.get_tools()
                stats[tool_type.value] = len(tools)
            except Exception as e:
                self.logger.error(f"获取{tool_type.value}工具统计时出错: {e}")
                stats[tool_type.value] = 0
        return stats

    def clear_mcp_tools(self):
        """清空所有MCP接入点工具（只影响MCP_ENDPOINT类型）"""
        if ToolType.MCP_ENDPOINT in self.executors:
            self.executors[ToolType.MCP_ENDPOINT].clear_tools()
        self._invalidate_cache()

    def register_mcp_tools(self, tools: list):
        """注册一批MCP接入点工具（只影响MCP_ENDPOINT类型）"""
        if ToolType.MCP_ENDPOINT in self.executors:
            self.executors[ToolType.MCP_ENDPOINT].register_tools(tools)
        self._invalidate_cache()

    def append_mcp_tools(self, tools: list):
        """追加MCP检索工具，智能去重避免与MCP接入点工具和其他工具重复"""
        if ToolType.MCP_ENDPOINT not in self.executors:
            self.logger.warning("MCP_ENDPOINT执行器未注册")
            return

        executor = self.executors[ToolType.MCP_ENDPOINT]

        # 获取当前已有的所有工具（包括MCP接入点工具和其他工具）
        current_all_tools = self.get_all_tools() if self._cached_tools else {}
        existing_tool_names = set(current_all_tools.keys())

        # 过滤掉已存在的工具，只添加新工具
        new_tools = []
        duplicate_tools = []

        for tool in tools:
            func = tool.get('function', {})
            tool_name = (func.get('fullName') or
                       func.get('name') or
                       tool.get('fullName', '') or
                       tool.get('name', ''))

            if tool_name:
                if tool_name not in existing_tool_names:
                    new_tools.append(tool)
                else:
                    duplicate_tools.append(tool_name)

        # 记录重复工具信息
        if duplicate_tools:
            self.logger.info(f"🔄 跳过 {len(duplicate_tools)} 个重复工具: {duplicate_tools[:5]}{'...' if len(duplicate_tools) > 5 else ''}")

        # 添加新工具
        if new_tools:
            # 使用执行器的 append_tools 方法，如果存在的话
            if hasattr(executor, 'append_tools'):
                executor.append_tools(new_tools)
            else:
                # 兼容旧版本，使用 register_tools
                executor.register_tools(new_tools)

            self._invalidate_cache()
            self.logger.info(f"✅ 成功追加 {len(new_tools)} 个新的MCP检索工具")

            # 输出新添加的工具名称
            new_tool_names = []
            for tool in new_tools:
                func = tool.get('function', {})
                tool_name = (func.get('fullName') or func.get('name') or
                           tool.get('fullName', '') or tool.get('name', ''))
                if tool_name:
                    new_tool_names.append(tool_name)

            if new_tool_names:
                self.logger.info(f"📋 新添加的工具: {new_tool_names[:3]}{'...' if len(new_tool_names) > 3 else ''}")
        else:
            self.logger.debug("没有新的MCP工具需要追加，所有工具都已存在")
