package xiaozhi.modules.config.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.fastjson2.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.common.utils.JsonUtils;
import xiaozhi.modules.agent.dao.AgentVoicePrintDao;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentPluginMapping;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.entity.AgentVoicePrintEntity;
import xiaozhi.modules.agent.service.*;
import xiaozhi.modules.agent.vo.AgentVoicePrintVO;
import xiaozhi.modules.agent.vo.AgentInfoVO;
import xiaozhi.modules.agent.dto.AgentDTO;
import xiaozhi.modules.agent.dto.McpcnUserInfoResponseDTO;
import xiaozhi.modules.config.service.ConfigService;
import xiaozhi.modules.config.vo.ComputerDeviceInfo;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.model.entity.ModelConfigEntity;
import xiaozhi.modules.model.service.ModelConfigService;
import xiaozhi.modules.sys.dto.SysParamsDTO;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.timbre.service.TimbreService;
import xiaozhi.modules.timbre.vo.TimbreDetailsVO;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import xiaozhi.grpc.device.DeviceServiceGrpc;
import xiaozhi.grpc.device.DeviceProto.GetDeviceByIDRequest;
import xiaozhi.grpc.device.DeviceProto.GetDeviceInfoResponse;
import xiaozhi.grpc.device.DeviceProto.GetMyDevicesRequest;
import xiaozhi.grpc.device.DeviceProto.GetMyDevicesResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.alibaba.fastjson2.JSONObject;

@Service
@AllArgsConstructor
public class ConfigServiceImpl implements ConfigService {
    private final SysParamsService sysParamsService;
    private final DeviceService deviceService;
    private final ModelConfigService modelConfigService;
    private final AgentService agentService;
    private final AgentTemplateService agentTemplateService;
    private final RedisUtils redisUtils;
    private final TimbreService timbreService;
    private final AgentPluginMappingService agentPluginMappingService;
    private final AgentMcpAccessPointService agentMcpAccessPointService;
    private final AgentVoicePrintDao agentVoicePrintDao;
    private final org.springframework.data.redis.core.StringRedisTemplate stringRedisTemplate;


    @Autowired
    private McpcnApiService mcpcnApiService;

    @Override
    public Object getConfig(Boolean isCache) {
        if (isCache) {
            // 先从Redis获取配置
            Object cachedConfig = redisUtils.get(RedisKeys.getServerConfigKey());
            if (cachedConfig != null) {
                return cachedConfig;
            }
        }

        // 构建配置信息
        Map<String, Object> result = new HashMap<>();
        buildConfig(result);

        // 查询默认智能体
        AgentTemplateEntity agent = agentTemplateService.getDefaultTemplate();
        if (agent == null) {
            throw new RenException("默认智能体未找到");
        }

        // 构建模块配置
        buildModuleConfig(
                null,
                null,
                null,
                null,
                null,
                null,
                agent.getVadModelId(),
                agent.getAsrModelId(),
                null,
                null,
                null,
                null,
                null,
                null,
                result,
                isCache);

        // 将配置存入Redis
        redisUtils.set(RedisKeys.getServerConfigKey(), result);

        return result;
    }

    @Override
    public Map<String, Object> getAgentModels(String macAddress, Map<String, String> selectedModule,String token) {
        // 1. 首先尝试获取整体缓存结果
        String cacheKey = RedisKeys.getAgentModelsKey(macAddress, selectedModule);
        @SuppressWarnings("unchecked")
        Map<String, Object> cachedResult = (Map<String, Object>) redisUtils.get(cacheKey);
        if (cachedResult != null) {
            // 在return之前从Redis获取额外的配置信息（不包含在缓存中）
            addExtraConfigFromRedis(cachedResult);
            return cachedResult;
        }

        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        // 2. 根据MAC地址查找硬件设备（优先从缓存获取）
        DeviceEntity device = getDeviceFromCache(macAddress);

        AgentInfoVO agent = null;
        ComputerDeviceInfo computerDeviceInfo = null;

        if (device == null) {
            // 如果硬件设备不存在，先尝试从缓存获取gRPC查询结果
            computerDeviceInfo = getComputerDeviceInfoFromCache(macAddress);
            if (computerDeviceInfo != null) {
                // 情况2：电脑设备（无硬件设备记录）- 进行鉴权
                if (StringUtils.isNotBlank(token)) {
                    // 调用外部接口获取用户信息
                    McpcnUserInfoResponseDTO userInfoResponse = mcpcnApiService.getUserInfo(token);

                    // 如果外部接口调用失败，抛出异常
                    if (userInfoResponse.getCode() != 0) {
                        throw new RenException(userInfoResponse.getCode(), userInfoResponse.getMsg());
                    }

                    // 鉴权成功，继续原有逻辑
                }

                // 如果设备有userId，通过用户ID和设备ID查询对应的智能体
                if (computerDeviceInfo.getUserId() > 0) {
                    // 通过用户ID和设备ID查询智能体（优先从缓存获取）
                    AgentEntity agentByUserAndDevice = getAgentByUserIdAndDeviceIdFromCache(
                        computerDeviceInfo.getUserId(), computerDeviceInfo.getDeviceId());
                    if (agentByUserAndDevice != null) {
                        agent = getAgentInfoFromCache(agentByUserAndDevice.getId());
                    } else {
                        // 如果没有找到用户和设备对应的智能体，则查询用户的智能体列表（优先从缓存获取）
                        List<AgentDTO> userAgents = getUserAgentsFromCache(computerDeviceInfo.getUserId());
                        if (userAgents != null && !userAgents.isEmpty()) {
                            // 使用第一个agent的ID获取完整的agent信息
                            agent = getAgentInfoFromCache(userAgents.get(0).getId());
                        }
                    }
                }

                // 如果没有找到用户agent，使用默认逻辑
                if (agent == null) {
                    // 如果是电脑设备但没有用户agent，返回默认智能体的配置，并包含设备信息
                    result = getDefaultAgentModels(selectedModule);
                    result.put("computer_device", computerDeviceInfo);
                    // 在return之前从Redis获取额外的配置信息（不包含在缓存中）
                    addExtraConfigFromRedis(result);
                    return result;
                }
            } else {
                // 如果不是电脑设备，去redis里看看有没有需要连接的设备
                String cachedCode = deviceService.geCodeByDeviceId(macAddress);
                if (StringUtils.isNotBlank(cachedCode)) {
                    throw new RenException(ErrorCode.OTA_DEVICE_NEED_BIND, cachedCode);
                }
                throw new RenException(ErrorCode.OTA_DEVICE_NOT_FOUND, "not found device");
            }

            // 获取mcp接入点地址
            String mcpEndpoint = agentMcpAccessPointService.getAgentMcpAccessAddress(agent.getId());
            if (StringUtils.isNotBlank(mcpEndpoint) && mcpEndpoint.startsWith("ws")) {
                mcpEndpoint = mcpEndpoint.replace("/mcp/", "/call/");
                result.put("mcp_endpoint", mcpEndpoint);
            }
        } else {
            // 获取智能体信息 - 对于硬件设备，使用原来的查询逻辑（优先从缓存获取）
            agent = getAgentInfoFromCache(device.getAgentId());
            if (agent == null) {
                throw new RenException("智能体未找到");
            }

            // 对于硬件设备，查询用户的所有设备列表
            if (agent.getUserId() != null) {
                List<ComputerDeviceInfo> userDevices = getUserDevicesFromCache(agent.getUserId());
                if (userDevices != null && !userDevices.isEmpty()) {
                    result.put("user_devices", userDevices);

                    // 循环集合，优先获取默认设备的mcpAccessAddress
                    String mcpEndpoint = null;
                    ComputerDeviceInfo defaultComputerDevice = null;

                    // 先查找默认设备
                    for (ComputerDeviceInfo deviceInfo : userDevices) {
                        if (deviceInfo.isDefaultDevice()) {
                            defaultComputerDevice = deviceInfo;
                            if (StringUtils.isNotBlank(deviceInfo.getMcpAccessAddress())) {
                                mcpEndpoint = deviceInfo.getMcpAccessAddress();
                                break;
                            }
                        }
                    }

                    // 如果没有找到默认设备的mcpAccessAddress，使用第一个有mcpAccessAddress的设备
                    if (StringUtils.isBlank(mcpEndpoint)) {
                        for (ComputerDeviceInfo deviceInfo : userDevices) {
                            if (StringUtils.isNotBlank(deviceInfo.getMcpAccessAddress())) {
                                mcpEndpoint = deviceInfo.getMcpAccessAddress();
                                break;
                            }
                        }
                    }

                    // 如果从设备列表中找到了mcpAccessAddress，进行处理
                    if (StringUtils.isNotBlank(mcpEndpoint) && mcpEndpoint.startsWith("ws")) {
                        mcpEndpoint = mcpEndpoint.replace("/mcp/", "/call/");
                        result.put("mcp_endpoint", mcpEndpoint);
                    }

                    // 为硬件设备添加默认的电脑设备信息
                    if (defaultComputerDevice != null) {
                        result.put("computer_device", defaultComputerDevice);
                    } else if (!userDevices.isEmpty()) {
                        // 如果没有默认设备，使用第一个设备作为电脑设备信息
                        result.put("computer_device", userDevices.get(0));
                    }
                }

                // 如果用户设备列表中没有找到mcpAccessAddress，使用原来的逻辑作为备用
                if (!result.containsKey("mcp_endpoint")) {
                    String mcpEndpoint = agentMcpAccessPointService.getAgentMcpAccessAddress(agent.getId());
                    if (StringUtils.isNotBlank(mcpEndpoint) && mcpEndpoint.startsWith("ws")) {
                        mcpEndpoint = mcpEndpoint.replace("/mcp/", "/call/");
                        result.put("mcp_endpoint", mcpEndpoint);
                    }
                }
            }
        }

        // 统一处理agent信息
        // 添加null检查，防止NullPointerException
        if (agent == null) {
            throw new RenException("智能体信息未找到");
        }

        // 获取音色信息（优先从缓存获取）
        String voice = null;
        String referenceAudio = null;
        String referenceText = null;
        TimbreDetailsVO timbre = getTimbreFromCache(agent.getTtsVoiceId());
        if (timbre != null) {
            voice = timbre.getTtsVoice();
            referenceAudio = timbre.getReferenceAudio();
            referenceText = timbre.getReferenceText();
        }

        // 如果是电脑设备，添加设备信息
        if (computerDeviceInfo != null) {
            result.put("computer_device", computerDeviceInfo);
        }
        // 获取单台设备每天最多输出字数（优先从缓存获取）
        String deviceMaxOutputSize = getSysParamFromCache("device_max_output_size");
        result.put("device_max_output_size", deviceMaxOutputSize);

        // 获取聊天记录配置
        Integer chatHistoryConf = agent.getChatHistoryConf();
        if (agent.getMemModelId() != null && agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.IGNORE.getCode();
        } else if (agent.getMemModelId() != null
                && !agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)
                && agent.getChatHistoryConf() == null) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode();
        }
        result.put("chat_history_conf", chatHistoryConf);
        // 如果客户端已实例化模型，则不返回
        String alreadySelectedVadModelId = (String) selectedModule.get("VAD");
        if (alreadySelectedVadModelId != null && alreadySelectedVadModelId.equals(agent.getVadModelId())) {
            agent.setVadModelId(null);
        }
        String alreadySelectedAsrModelId = (String) selectedModule.get("ASR");
        if (alreadySelectedAsrModelId != null && alreadySelectedAsrModelId.equals(agent.getAsrModelId())) {
            agent.setAsrModelId(null);
        }

        // 添加函数调用参数信息（优先从缓存获取）
        if (!Objects.equals(agent.getIntentModelId(), "Intent_nointent")) {
            String agentId = agent.getId();
            List<AgentPluginMapping> pluginMappings = getAgentPluginMappingsFromCache(agentId);
            if (pluginMappings != null && !pluginMappings.isEmpty()) {
                Map<String, Object> pluginParams = new HashMap<>();
                for (AgentPluginMapping pluginMapping : pluginMappings) {
                    pluginParams.put(pluginMapping.getProviderCode(), pluginMapping.getParamInfo());
                }
                result.put("plugins", pluginParams);
            }
        }
        // 获取声纹信息
        buildVoiceprintConfig(agent.getId(), result);
        Object voiceprintObj = result.get("voiceprint");
        if (voiceprintObj != null) {
            Map<String, Object> voiceprint = (Map<String, Object>) voiceprintObj;
            voiceprint.put("voiceprintInterruptEnabled", agent.getVoiceprintInterruptEnabled());
        } else {
            // 如果声纹信息为空，创建一个新的Map并设置基本信息
            Map<String, Object> voiceprint = new HashMap<>();
            voiceprint.put("voiceprintInterruptEnabled", agent.getVoiceprintInterruptEnabled());
            result.put("voiceprint", voiceprint);
        }
        // 构建模块配置
        buildModuleConfig(
                agent.getAgentName(),
                agent.getSystemPrompt(),
                agent.getSummaryMemory(),
                voice,
                referenceAudio,
                referenceText,
                agent.getVadModelId(),
                agent.getAsrModelId(),
                agent.getLlmModelId(),
                agent.getVllmModelId(),
                agent.getTtsModelId(),
                agent.getMemModelId(),
                agent.getIntentModelId(),
                agent.getUserId(),
                result,
                true);

        // 将整体结果存入缓存
        redisUtils.set(cacheKey, result);

        // 在return之前从Redis获取额外的配置信息（不包含在缓存中）
        addExtraConfigFromRedis(result);

        return result;
    }

    /**
     * 构建配置信息
     * 
     * @param config 系统参数列表
     * @return 配置信息
     */
    private Object buildConfig(Map<String, Object> config) {
        // 尝试从缓存获取系统参数列表
        String sysParamsListCacheKey = "sys:params:list";
        @SuppressWarnings("unchecked")
        List<SysParamsDTO> paramsList = (List<SysParamsDTO>) redisUtils.get(sysParamsListCacheKey);
        if (paramsList == null) {
            // 缓存未命中，查询数据库
            paramsList = sysParamsService.list(new HashMap<>());
        }

        for (SysParamsDTO param : paramsList) {
            String[] keys = param.getParamCode().split("\\.");
            Map<String, Object> current = config;

            // 遍历除最后一个key之外的所有key
            for (int i = 0; i < keys.length - 1; i++) {
                String key = keys[i];
                if (!current.containsKey(key)) {
                    current.put(key, new HashMap<String, Object>());
                }
                current = (Map<String, Object>) current.get(key);
            }

            // 处理最后一个key
            String lastKey = keys[keys.length - 1];
            String value = param.getParamValue();

            // 根据valueType转换值
            switch (param.getValueType().toLowerCase()) {
                case "number":
                    try {
                        double doubleValue = Double.parseDouble(value);
                        // 如果数值是整数形式，则转换为Integer
                        if (doubleValue == (int) doubleValue) {
                            current.put(lastKey, (int) doubleValue);
                        } else {
                            current.put(lastKey, doubleValue);
                        }
                    } catch (NumberFormatException e) {
                        current.put(lastKey, value);
                    }
                    break;
                case "boolean":
                    current.put(lastKey, Boolean.parseBoolean(value));
                    break;
                case "array":
                    // 将分号分隔的字符串转换为数字数组
                    List<String> list = new ArrayList<>();
                    for (String num : value.split(";")) {
                        if (StringUtils.isNotBlank(num)) {
                            list.add(num.trim());
                        }
                    }
                    current.put(lastKey, list);
                    break;
                case "json":
                    try {
                        current.put(lastKey, JsonUtils.parseObject(value, Object.class));
                    } catch (Exception e) {
                        current.put(lastKey, value);
                    }
                    break;
                default:
                    current.put(lastKey, value);
            }
        }

        return config;
    }

    /**
     * 构建声纹配置信息
     * 
     * @param agentId 智能体ID
     * @param result  结果Map
     */
    private void buildVoiceprintConfig(String agentId, Map<String, Object> result) {
        try {
            // 获取声纹接口地址（优先从缓存获取）
            String voiceprintUrl = getSysParamFromCache("server.voice_print");
            if (StringUtils.isBlank(voiceprintUrl) || "null".equals(voiceprintUrl)) {
                return;
            }

            // 获取智能体关联的声纹信息（优先从缓存获取）
            List<AgentVoicePrintVO> voiceprints = getAgentVoiceprintsFromCache(agentId);
            if (voiceprints == null || voiceprints.isEmpty()) {
                return;
            }

            // 构建speakers列表
            List<String> speakers = new ArrayList<>();
            for (AgentVoicePrintVO voiceprint : voiceprints) {
                String speakerStr = String.format("%s,%s,%s",
                        voiceprint.getId(),
                        voiceprint.getSourceName(),
                        voiceprint.getIntroduce() != null ? voiceprint.getIntroduce() : "");
                speakers.add(speakerStr);
            }

            // 构建声纹配置
            Map<String, Object> voiceprintConfig = new HashMap<>();
            voiceprintConfig.put("url", voiceprintUrl);
            voiceprintConfig.put("speakers", speakers);

            result.put("voiceprint", voiceprintConfig);
        } catch (Exception e) {
            // 声纹配置获取失败时不影响其他功能
            System.err.println("获取声纹配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取智能体关联的声纹信息
     * 
     * @param agentId 智能体ID
     * @return 声纹信息列表
     */
    private List<AgentVoicePrintVO> getVoiceprintsByAgentId(String agentId) {
        LambdaQueryWrapper<AgentVoicePrintEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentVoicePrintEntity::getAgentId, agentId);
        queryWrapper.orderByAsc(AgentVoicePrintEntity::getCreateDate);
        List<AgentVoicePrintEntity> entities = agentVoicePrintDao.selectList(queryWrapper);
        return ConvertUtils.sourceToTarget(entities, AgentVoicePrintVO.class);
    }

    /**
     * 构建模块配置
     * 
     * @param prompt         提示词
     * @param voice          音色
     * @param referenceAudio 参考音频路径
     * @param referenceText  参考文本
     * @param vadModelId     VAD模型ID
     * @param asrModelId     ASR模型ID
     * @param llmModelId     LLM模型ID
     * @param ttsModelId     TTS模型ID
     * @param memModelId     记忆模型ID
     * @param intentModelId  意图模型ID
     * @param userId  用户ID
     * @param result         结果Map
     */
    private void buildModuleConfig(
            String assistantName,
            String prompt,
            String summaryMemory,
            String voice,
            String referenceAudio,
            String referenceText,
            String vadModelId,
            String asrModelId,
            String llmModelId,
            String vllmModelId,
            String ttsModelId,
            String memModelId,
            String intentModelId,
            Long userId,
            Map<String, Object> result,
            boolean isCache) {
        Map<String, String> selectedModule = new HashMap<>();

        String[] modelTypes = { "VAD", "ASR", "TTS", "Memory", "Intent", "LLM", "VLLM" };
        String[] modelIds = { vadModelId, asrModelId, ttsModelId, memModelId, intentModelId, llmModelId, vllmModelId };
        String intentLLMModelId = null;
        String memLocalShortLLMModelId = null;

        for (int i = 0; i < modelIds.length; i++) {
            if (modelIds[i] == null) {
                continue;
            }
            // 优先从缓存获取模型配置
            ModelConfigEntity model = getModelConfigFromCache(modelIds[i]);
            if (model == null) {
                continue;
            }
            Map<String, Object> typeConfig = new HashMap<>();
            if (model.getConfigJson() != null) {
                typeConfig.put(model.getId(), model.getConfigJson());
                // 如果是TTS类型，添加private_voice属性
                if ("TTS".equals(modelTypes[i])) {
                    if (voice != null)
                        ((Map<String, Object>) model.getConfigJson()).put("private_voice", voice);
                    if (referenceAudio != null)
                        ((Map<String, Object>) model.getConfigJson()).put("ref_audio", referenceAudio);
                    if (referenceText != null)
                        ((Map<String, Object>) model.getConfigJson()).put("ref_text", referenceText);
                }
                // 如果是Intent类型，且type=intent_llm，则给他添加附加模型
                if ("Intent".equals(modelTypes[i])) {
                    Map<String, Object> map = (Map<String, Object>) model.getConfigJson();
                    if ("intent_llm".equals(map.get("type"))) {
                        intentLLMModelId = (String) map.get("llm");
                        if (StringUtils.isNotBlank(intentLLMModelId) && intentLLMModelId.equals(llmModelId)) {
                            intentLLMModelId = null;
                        }
                    }
                    if (map.get("functions") != null) {
                        String functionStr = (String) map.get("functions");
                        if (StringUtils.isNotBlank(functionStr)) {
                            String[] functions = functionStr.split("\\;");
                            map.put("functions", functions);
                        }
                    }
                    System.out.println("map: " + map);
                }
                if ("Memory".equals(modelTypes[i])) {
                    Map<String, Object> map = (Map<String, Object>) model.getConfigJson();
                    if ("mem_local_short".equals(map.get("type"))) {
                        memLocalShortLLMModelId = (String) map.get("llm");
                        if (StringUtils.isNotBlank(memLocalShortLLMModelId)
                                && memLocalShortLLMModelId.equals(llmModelId)) {
                            memLocalShortLLMModelId = null;
                        }
                    }
                }
                // 如果是LLM类型，且intentLLMModelId不为空，则添加附加模型
                if ("LLM".equals(modelTypes[i])) {
                    if (StringUtils.isNotBlank(intentLLMModelId)) {
                        if (!typeConfig.containsKey(intentLLMModelId)) {
                            // 优先从缓存获取模型配置
                            ModelConfigEntity intentLLM = getModelConfigFromCache(intentLLMModelId);
                            if (intentLLM != null) {
                                typeConfig.put(intentLLM.getId(), intentLLM.getConfigJson());
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(memLocalShortLLMModelId)) {
                        if (!typeConfig.containsKey(memLocalShortLLMModelId)) {
                            // 优先从缓存获取模型配置
                            ModelConfigEntity memLocalShortLLM = getModelConfigFromCache(memLocalShortLLMModelId);
                            if (memLocalShortLLM != null) {
                                typeConfig.put(memLocalShortLLM.getId(), memLocalShortLLM.getConfigJson());
                            }
                        }
                    }
                }
            }
            result.put(modelTypes[i], typeConfig);

            selectedModule.put(modelTypes[i], model.getId());
        }

        result.put("selected_module", selectedModule);
        if (StringUtils.isNotBlank(prompt)) {
            prompt = prompt.replace("{{assistant_name}}", StringUtils.isBlank(assistantName) ? "小智" : assistantName);
        }
        result.put("prompt", prompt);
        result.put("summaryMemory", summaryMemory);
        result.put("userId", userId);
    }

    /**
     * 调用gRPC接口查询电脑设备信息
     *
     * @param macAddress MAC地址
     * @return 设备信息对象，如果未找到则返回null
     */
    private ComputerDeviceInfo queryComputerDeviceInfoFromGrpc(String macAddress) {
        // gRPC客户端调用Go端服务，查询电脑设备信息
        ManagedChannel channel = null;
        try {
            channel = ManagedChannelBuilder.forAddress("localhost", 10000)
                    .usePlaintext()
                    .build();
            DeviceServiceGrpc.DeviceServiceBlockingStub stub = DeviceServiceGrpc.newBlockingStub(channel);
            GetDeviceByIDRequest request = GetDeviceByIDRequest.newBuilder()
                    .setDeviceId(macAddress)
                    .build();
            GetDeviceInfoResponse response = stub.getDeviceInfo(request);
            if (response != null && response.getBase().getSuccess() && response.hasDevice()) {
                // 构建设备信息对象
                ComputerDeviceInfo deviceInfo = new ComputerDeviceInfo();

                // 设备基本信息
                xiaozhi.grpc.device.DeviceProto.Device device = response.getDevice();
                deviceInfo.setId(device.getId());
                deviceInfo.setDeviceId(device.getDeviceId());
                deviceInfo.setDeviceName(device.getDeviceName());
                deviceInfo.setHardwareHash(device.getHardwareHash());
                deviceInfo.setCpuInfo(device.getCpuInfo());
                deviceInfo.setMemoryInfo(device.getMemoryInfo());
                deviceInfo.setDiskInfo(device.getDiskInfo());
                deviceInfo.setNetworkInfo(device.getNetworkInfo());
                deviceInfo.setGpuInfo(device.getGpuInfo());
                deviceInfo.setOsName(device.getOsName());
                deviceInfo.setOsVersion(device.getOsVersion());
                deviceInfo.setOsArch(device.getOsArch());
                deviceInfo.setHostname(device.getHostname());
                deviceInfo.setUsername(device.getUsername());
                deviceInfo.setUserHomeDir(device.getUserHomeDir());
                deviceInfo.setWorkDir(device.getWorkDir());
                deviceInfo.setAppVersion(device.getAppVersion());
                deviceInfo.setAppBuildNo(device.getAppBuildNo());
                deviceInfo.setIpAddress(device.getIpAddress());
                deviceInfo.setMacAddress(device.getMacAddress());
                deviceInfo.setUserId(device.getUserId());
                deviceInfo.setStatus(device.getStatus());
                deviceInfo.setActive(device.getIsActive());
                deviceInfo.setReportCount(device.getReportCount());
                deviceInfo.setRemark(device.getRemark());
                deviceInfo.setDefaultDevice(device.getIsDefault());
                // 注意：mcpAccessAddress 和 agentId 字段在 gRPC Device 对象中不存在
                // 这些字段需要从其他地方获取或设置为默认值
                deviceInfo.setMcpAccessAddress(null);
                deviceInfo.setAgentId(null);

                // 在线状态信息
                if (response.hasOnlineStatus()) {
                    xiaozhi.grpc.device.DeviceProto.DeviceOnlineStatus onlineStatus = response.getOnlineStatus();
                    ComputerDeviceInfo.OnlineStatus status = new ComputerDeviceInfo.OnlineStatus();
                    status.setOnline(onlineStatus.getIsOnline());
                    status.setSessionType(onlineStatus.getSessionType());
                    status.setIpAddress(onlineStatus.getIpAddress());
                    status.setUserAgent(onlineStatus.getUserAgent());
                    status.setDeviceName(onlineStatus.getDeviceName());
                    status.setOsInfo(onlineStatus.getOsInfo());
                    status.setAppVersion(onlineStatus.getAppVersion());
                    deviceInfo.setOnlineStatus(status);
                }

                return deviceInfo;
            }
        } catch (Exception e) {
            System.err.println("gRPC查询电脑设备信息失败: " + e.getMessage());
        } finally {
            if (channel != null) {
                channel.shutdown();
            }
        }
        return null;
    }

    /**
     * 查询用户设备列表（混合方案：优先gRPC，后备数据库查询）
     *
     * @param userId 用户ID
     * @return 用户设备列表，如果未找到则返回null
     */
    private List<ComputerDeviceInfo> queryUserDevicesFromGrpc(Long userId) {
        // 方案1：尝试gRPC调用（当proto重新生成后启用）
        List<ComputerDeviceInfo> grpcResult = tryQueryUserDevicesFromGrpc(userId);
        if (grpcResult != null && !grpcResult.isEmpty()) {
            return grpcResult;
        }

        // 方案2：后备方案 - 使用数据库查询结合单个设备的gRPC查询
        return queryUserDevicesFromDatabase(userId);
    }

    /**
     * 尝试通过gRPC GetMyDevices接口查询用户设备列表
     *
     * @param userId 用户ID
     * @return 设备列表，失败时返回null
     */
    private List<ComputerDeviceInfo> tryQueryUserDevicesFromGrpc(Long userId) {
        ManagedChannel channel = null;
        try {
            channel = ManagedChannelBuilder.forAddress("localhost", 10000)
                    .usePlaintext()
                    .build();
            DeviceServiceGrpc.DeviceServiceBlockingStub stub = DeviceServiceGrpc.newBlockingStub(channel);
            GetMyDevicesRequest request = GetMyDevicesRequest.newBuilder()
                    .setUserId(userId.intValue())
                    .build();
            GetMyDevicesResponse response = stub.getMyDevices(request);

            if (response != null && response.getBase().getSuccess() && response.getDevicesCount() > 0) {
                List<ComputerDeviceInfo> deviceList = new ArrayList<>();

                for (xiaozhi.grpc.device.DeviceProto.Device device : response.getDevicesList()) {
                    ComputerDeviceInfo deviceInfo = convertGrpcDeviceToComputerDeviceInfo(device);
                    if (deviceInfo != null) {
                        deviceList.add(deviceInfo);
                    }
                }

                return deviceList.isEmpty() ? null : deviceList;
            }
        } catch (Exception e) {
            System.err.println("gRPC查询用户设备列表失败: " + e.getMessage());
        } finally {
            if (channel != null) {
                channel.shutdown();
            }
        }

        return null;
    }

    /**
     * 通过数据库查询用户设备列表（后备方案）
     *
     * @param userId 用户ID
     * @return 设备列表
     */
    private List<ComputerDeviceInfo> queryUserDevicesFromDatabase(Long userId) {
        try {
            // 获取用户的所有智能体
            List<AgentDTO> userAgents = agentService.getUserAgents(userId);
            if (userAgents == null || userAgents.isEmpty()) {
                System.out.println("用户 " + userId + " 没有关联的智能体");
                return null;
            }

            List<ComputerDeviceInfo> allDevices = new ArrayList<>();

            // 遍历用户的每个智能体，获取关联的设备
            for (AgentDTO agent : userAgents) {
                List<DeviceEntity> devices = deviceService.getUserDevices(userId, agent.getId());
                if (devices != null && !devices.isEmpty()) {
                    // 将DeviceEntity转换为ComputerDeviceInfo
                    for (DeviceEntity device : devices) {
                        ComputerDeviceInfo deviceInfo = convertDeviceEntityToComputerDeviceInfo(device);
                        if (deviceInfo != null) {
                            // 尝试通过gRPC获取更详细的设备信息
                            ComputerDeviceInfo detailedInfo = queryComputerDeviceInfoFromGrpc(device.getMacAddress());
                            if (detailedInfo != null) {
                                // 使用gRPC获取的详细信息，但保留数据库中的关联信息
                                detailedInfo.setUserId(device.getUserId());
                                detailedInfo.setAgentId(device.getAgentId());
                                allDevices.add(detailedInfo);
                            } else {
                                // 如果gRPC查询失败，使用数据库信息
                                allDevices.add(deviceInfo);
                            }
                        }
                    }
                }
            }

            return allDevices.isEmpty() ? null : allDevices;

        } catch (Exception e) {
            System.err.println("数据库查询用户设备列表失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 将DeviceEntity转换为ComputerDeviceInfo
     *
     * @param device 设备实体
     * @return 计算机设备信息
     */
    private ComputerDeviceInfo convertDeviceEntityToComputerDeviceInfo(DeviceEntity device) {
        if (device == null) {
            return null;
        }

        ComputerDeviceInfo deviceInfo = new ComputerDeviceInfo();
        deviceInfo.setDeviceId(device.getId());
        deviceInfo.setMacAddress(device.getMacAddress());
        deviceInfo.setUserId(device.getUserId());
        deviceInfo.setAgentId(device.getAgentId());
        deviceInfo.setAppVersion(device.getAppVersion());
        deviceInfo.setDeviceName(device.getAlias()); // 使用别名作为设备名称
        deviceInfo.setActive(true); // 数据库中的设备默认为活跃状态
        deviceInfo.setDefaultDevice(false); // 默认值，可以根据业务逻辑调整

        // 其他字段设置为默认值或空值
        deviceInfo.setId(0L);
        deviceInfo.setStatus(1); // 正常状态
        deviceInfo.setReportCount(0L);

        return deviceInfo;
    }

    /**
     * 将gRPC Device对象转换为ComputerDeviceInfo
     *
     * @param device gRPC设备对象
     * @return 计算机设备信息
     */
    private ComputerDeviceInfo convertGrpcDeviceToComputerDeviceInfo(xiaozhi.grpc.device.DeviceProto.Device device) {
        if (device == null) {
            return null;
        }

        ComputerDeviceInfo deviceInfo = new ComputerDeviceInfo();

        // 设备基本信息
        deviceInfo.setId(device.getId());
        deviceInfo.setDeviceId(device.getDeviceId());
        deviceInfo.setDeviceName(device.getDeviceName());
        deviceInfo.setHardwareHash(device.getHardwareHash());
        deviceInfo.setCpuInfo(device.getCpuInfo());
        deviceInfo.setMemoryInfo(device.getMemoryInfo());
        deviceInfo.setDiskInfo(device.getDiskInfo());
        deviceInfo.setNetworkInfo(device.getNetworkInfo());
        deviceInfo.setGpuInfo(device.getGpuInfo());
        deviceInfo.setOsName(device.getOsName());
        deviceInfo.setOsVersion(device.getOsVersion());
        deviceInfo.setOsArch(device.getOsArch());
        deviceInfo.setHostname(device.getHostname());
        deviceInfo.setUsername(device.getUsername());
        deviceInfo.setUserHomeDir(device.getUserHomeDir());
        deviceInfo.setWorkDir(device.getWorkDir());
        deviceInfo.setAppVersion(device.getAppVersion());
        deviceInfo.setAppBuildNo(device.getAppBuildNo());
        deviceInfo.setIpAddress(device.getIpAddress());
        deviceInfo.setMacAddress(device.getMacAddress());
        deviceInfo.setUserId(device.getUserId());
        deviceInfo.setStatus(device.getStatus());
        deviceInfo.setActive(device.getIsActive());
        deviceInfo.setReportCount(device.getReportCount());
        deviceInfo.setRemark(device.getRemark());
        deviceInfo.setDefaultDevice(device.getIsDefault());
        deviceInfo.setMcpAccessAddress(device.getMcpAccessAddress());
        deviceInfo.setAgentId(device.getAgentId());

        return deviceInfo;
    }

    /**
     * 获取默认智能体的模型配置
     *
     * @param selectedModule 客户端已实例化的模型
     * @return 默认智能体的模型配置
     */
    private Map<String, Object> getDefaultAgentModels(Map<String, String> selectedModule) {
        // 查询默认智能体模板
        AgentTemplateEntity defaultTemplate = agentTemplateService.getDefaultTemplate();
        if (defaultTemplate == null) {
            throw new RenException("默认智能体模板未找到");
        }

        // 构建返回数据
        Map<String, Object> result = new HashMap<>();

        // 获取单台设备每天最多输出字数
        String deviceMaxOutputSize = sysParamsService.getValue("device_max_output_size", true);
        result.put("device_max_output_size", deviceMaxOutputSize);

        // 设置默认聊天记录配置
        Integer chatHistoryConf = defaultTemplate.getChatHistoryConf();
        if (defaultTemplate.getMemModelId() != null && defaultTemplate.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.IGNORE.getCode();
        } else if (defaultTemplate.getMemModelId() != null
                && !defaultTemplate.getMemModelId().equals(Constant.MEMORY_NO_MEM)
                && defaultTemplate.getChatHistoryConf() == null) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode();
        }
        result.put("chat_history_conf", chatHistoryConf);

        // 如果客户端已实例化模型，则不返回
        String alreadySelectedVadModelId = (String) selectedModule.get("VAD");
        if (alreadySelectedVadModelId != null && alreadySelectedVadModelId.equals(defaultTemplate.getVadModelId())) {
            defaultTemplate.setVadModelId(null);
        }
        String alreadySelectedAsrModelId = (String) selectedModule.get("ASR");
        if (alreadySelectedAsrModelId != null && alreadySelectedAsrModelId.equals(defaultTemplate.getAsrModelId())) {
            defaultTemplate.setAsrModelId(null);
        }

        // 构建模块配置
        buildModuleConfig(
                null, // assistantName - 使用默认名称
                defaultTemplate.getSystemPrompt(),
                defaultTemplate.getSummaryMemory(),
                null, // voice - 使用默认音色
                null, // referenceAudio
                null, // referenceText
                defaultTemplate.getVadModelId(),
                defaultTemplate.getAsrModelId(),
                defaultTemplate.getLlmModelId(),
                defaultTemplate.getVllmModelId(),
                defaultTemplate.getTtsModelId(),
                defaultTemplate.getMemModelId(),
                defaultTemplate.getIntentModelId(),
                null,
                result,
                true);

        return result;
    }

    // ==================== 缓存查询辅助方法 ====================

    /**
     * 从缓存获取设备信息，缓存未命中时查询数据库并存入缓存
     */
    private DeviceEntity getDeviceFromCache(String macAddress) {
        String cacheKey = RedisKeys.getDeviceByMacKey(macAddress);
        DeviceEntity cachedDevice = (DeviceEntity) redisUtils.get(cacheKey);
        if (cachedDevice != null) {
            return cachedDevice;
        }
        // 缓存未命中，查询数据库
        DeviceEntity device = deviceService.getDeviceByMacAddress(macAddress);
        // 将查询结果存入缓存（即使为null也缓存，避免缓存穿透）
        if (device != null) {
            redisUtils.set(cacheKey, device);
        }
        return device;
    }

    /**
     * 从缓存获取电脑设备信息，缓存未命中时调用gRPC接口获取并存入缓存
     */
    private ComputerDeviceInfo getComputerDeviceInfoFromCache(String macAddress) {
        String deviceId = macAddress;
        String cacheKey = "device:device:" + deviceId;

        try {
            // 使用StringRedisTemplate获取纯字符串
            String jsonStr = getRedisStringValue(cacheKey);

            if (StringUtils.isNotBlank(jsonStr)) {
                // 使用JSONObject直接解析（代码更简洁）
                JSONObject jsonObj = JSONObject.parseObject(jsonStr);
                ComputerDeviceInfo deviceInfo = new ComputerDeviceInfo();

                // 安全地从JSONObject获取字段值，处理null值
                deviceInfo.setId(getLongSafely(jsonObj, "ID"));
                deviceInfo.setDeviceId(jsonObj.getString("deviceId"));
                deviceInfo.setDeviceName(jsonObj.getString("deviceName"));
                deviceInfo.setHardwareHash(jsonObj.getString("hardwareHash"));
                deviceInfo.setCpuInfo(jsonObj.getString("cpuInfo"));
                deviceInfo.setMemoryInfo(jsonObj.getString("memoryInfo"));
                deviceInfo.setDiskInfo(jsonObj.getString("diskInfo"));
                deviceInfo.setNetworkInfo(jsonObj.getString("networkInfo"));
                deviceInfo.setGpuInfo(jsonObj.getString("gpuInfo"));
                deviceInfo.setOsName(jsonObj.getString("osName"));
                deviceInfo.setOsVersion(jsonObj.getString("osVersion"));
                deviceInfo.setOsArch(jsonObj.getString("osArch"));
                deviceInfo.setHostname(jsonObj.getString("hostname"));
                deviceInfo.setUsername(jsonObj.getString("username"));
                deviceInfo.setUserHomeDir(jsonObj.getString("userHomeDir"));
                deviceInfo.setWorkDir(jsonObj.getString("workDir"));
                deviceInfo.setAppVersion(jsonObj.getString("appVersion"));
                deviceInfo.setAppBuildNo(jsonObj.getString("appBuildNo"));
                deviceInfo.setIpAddress(jsonObj.getString("ipAddress"));
                deviceInfo.setMacAddress(jsonObj.getString("macAddress"));
                deviceInfo.setUserId(getLongSafely(jsonObj, "userId"));
                deviceInfo.setStatus(getIntegerSafely(jsonObj, "status"));
                deviceInfo.setActive(getBooleanSafely(jsonObj, "isActive"));
                deviceInfo.setReportCount(getLongSafely(jsonObj, "reportCount"));
                deviceInfo.setRemark(jsonObj.getString("remark"));
                deviceInfo.setDefaultDevice(getBooleanSafely(jsonObj, "isDefault"));
                deviceInfo.setMcpAccessAddress(jsonObj.getString("mcpAccessAddress"));
                deviceInfo.setAgentId(jsonObj.getString("agentId"));

                return deviceInfo;
            }

        } catch (Exception e) {
            System.err.println("从Redis获取设备信息失败: " + e.getMessage());
        }

        // 缓存未命中，调用gRPC接口获取设备信息
        ComputerDeviceInfo deviceInfo = queryComputerDeviceInfoFromGrpc(macAddress);

        // 如果gRPC查询成功，将结果存入缓存
        if (deviceInfo != null) {
            try {
                // 将设备信息转换为JSON并存入Redis缓存
                String deviceJson = JSONObject.toJSONString(deviceInfo);
                stringRedisTemplate.opsForValue().set(cacheKey, deviceJson);
            } catch (Exception e) {
                System.err.println("存储设备信息到缓存失败: " + e.getMessage());
            }
        }

        return deviceInfo;
    }

    /**
     * 直接从Redis获取字符串值，避免Jackson反序列化
     */
    private String getRedisStringValue(String key) {
        try {
            // 直接使用StringRedisTemplate获取字符串，避免Jackson反序列化
            return stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            System.err.println("StringRedisTemplate获取数据失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从缓存获取用户设备列表，缓存未命中时调用gRPC接口获取并存入缓存
     */
    private List<ComputerDeviceInfo> getUserDevicesFromCache(Long userId) {
        String cacheKey = "device:user_devices:" + userId;

        try {
            // 使用StringRedisTemplate获取JSON字符串
            String jsonStr = getRedisStringValue(cacheKey);
            if (StringUtils.isNotBlank(jsonStr)) {
                // 使用JSONObject解析为ComputerDeviceInfo列表
                List<ComputerDeviceInfo> userDevices = JSONArray.parseArray(jsonStr, ComputerDeviceInfo.class);
                if (userDevices != null && !userDevices.isEmpty()) {
                    return userDevices;
                }
            }
        } catch (Exception e) {
            System.err.println("从缓存获取用户设备列表失败: " + e.getMessage());
        }

        // 缓存未命中，调用gRPC接口获取用户设备列表
        List<ComputerDeviceInfo> userDevices = queryUserDevicesFromGrpc(userId);

        // 如果gRPC查询成功，将结果存入缓存
        if (userDevices != null && !userDevices.isEmpty()) {
            try {
                // 将设备列表转换为JSON并存入Redis缓存
                String devicesJson = JSONArray.toJSONString(userDevices);
                stringRedisTemplate.opsForValue().set(cacheKey, devicesJson);
            } catch (Exception e) {
                System.err.println("存储用户设备列表到缓存失败: " + e.getMessage());
            }
        }

        return userDevices;
    }

    /**
     * 从缓存获取智能体信息，缓存未命中时查询数据库并存入缓存
     */
    private AgentInfoVO getAgentInfoFromCache(String agentId) {
        String cacheKey = RedisKeys.getAgentInfoKey(agentId);

        try {
            // 尝试从StringRedisTemplate获取JSON字符串
            String jsonStr = getRedisStringValue(cacheKey);
            if (StringUtils.isNotBlank(jsonStr)) {
                // 使用手动解析，避免Jackson类型问题
                AgentInfoVO cachedAgent = parseAgentInfoFromJson(jsonStr);
                if (cachedAgent != null) {
                    return cachedAgent;
                }
            }
        } catch (Exception e) {
            System.err.println("从StringRedisTemplate获取智能体信息失败: " + e.getMessage());
        }

        // 如果缓存获取失败，尝试使用原来的方式
        try {
            AgentInfoVO cachedAgent = (AgentInfoVO) redisUtils.get(cacheKey);
            if (cachedAgent != null) {
                return cachedAgent;
            }
        } catch (Exception e) {
            System.err.println("使用redisUtils获取智能体信息也失败: " + e.getMessage());
        }

        // 缓存未命中，查询数据库
        AgentInfoVO agent = agentService.getAgentById(agentId);
        // 将查询结果存入缓存
        if (agent != null) {
            redisUtils.set(cacheKey, agent);
        }
        return agent;
    }

    /**
     * 手动解析智能体信息JSON，避免Jackson类型问题
     */
    private AgentInfoVO parseAgentInfoFromJson(String jsonStr) {
        try {
            JSONObject jsonObj = JSONObject.parseObject(jsonStr);

            AgentInfoVO agentInfo = new AgentInfoVO();
            agentInfo.setId(jsonObj.getString("id"));
            agentInfo.setAgentName(jsonObj.getString("agentName"));
            agentInfo.setAgentCode(jsonObj.getString("agentCode"));
            agentInfo.setSystemPrompt(jsonObj.getString("systemPrompt"));
            agentInfo.setSummaryMemory(jsonObj.getString("summaryMemory"));
            agentInfo.setUserId(jsonObj.getLong("userId"));
            agentInfo.setAsrModelId(jsonObj.getString("asrModelId"));
            agentInfo.setVadModelId(jsonObj.getString("vadModelId"));
            agentInfo.setLlmModelId(jsonObj.getString("llmModelId"));
            agentInfo.setVllmModelId(jsonObj.getString("vllmModelId"));
            agentInfo.setTtsModelId(jsonObj.getString("ttsModelId"));
            agentInfo.setTtsVoiceId(jsonObj.getString("ttsVoiceId"));
            agentInfo.setMemModelId(jsonObj.getString("memModelId"));
            agentInfo.setIntentModelId(jsonObj.getString("intentModelId"));
            agentInfo.setChatHistoryConf(jsonObj.getInteger("chatHistoryConf"));
            agentInfo.setLangCode(jsonObj.getString("langCode"));
            agentInfo.setLanguage(jsonObj.getString("language"));
            agentInfo.setSort(jsonObj.getInteger("sort"));
            agentInfo.setVoiceprintInterruptEnabled(jsonObj.getBoolean("voiceprintInterruptEnabled"));
            agentInfo.setMcpAccessAddress(jsonObj.getString("mcpAccessAddress"));

            // 手动解析functions列表，避免Jackson类型问题
            com.alibaba.fastjson2.JSONArray functionsArray = jsonObj.getJSONArray("functions");
            if (functionsArray != null) {
                List<AgentPluginMapping> functions = new ArrayList<>();
                for (int i = 0; i < functionsArray.size(); i++) {
                    com.alibaba.fastjson2.JSONObject funcObj = functionsArray.getJSONObject(i);
                    AgentPluginMapping mapping = new AgentPluginMapping();
                    mapping.setId(funcObj.getLong("id"));
                    mapping.setAgentId(funcObj.getString("agentId"));
                    mapping.setPluginId(funcObj.getString("pluginId"));
                    mapping.setParamInfo(funcObj.getString("paramInfo"));
                    functions.add(mapping);
                }
                agentInfo.setFunctions(functions);
            }

            return agentInfo;
        } catch (Exception e) {
            System.err.println("手动解析智能体信息JSON失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从缓存获取用户智能体列表，缓存未命中时查询数据库并存入缓存
     */
    private List<AgentDTO> getUserAgentsFromCache(Long userId) {
        String cacheKey = RedisKeys.getUserAgentsKey(userId);

        try {
            // 尝试从StringRedisTemplate获取JSON字符串
            String jsonStr = getRedisStringValue(cacheKey);
            if (StringUtils.isNotBlank(jsonStr)) {
                // 使用JSONObject直接解析为List
                List<AgentDTO> cachedAgents = JSONArray.parseArray(jsonStr, AgentDTO.class);
                if (cachedAgents != null) {
                    return cachedAgents;
                }
            }
        } catch (Exception e) {
            System.err.println("从缓存获取用户智能体列表失败: " + e.getMessage());
        }

        // 如果缓存获取失败，尝试使用原来的方式
        try {
            @SuppressWarnings("unchecked")
            List<AgentDTO> cachedAgents = (List<AgentDTO>) redisUtils.get(cacheKey);
            if (cachedAgents != null) {
                return cachedAgents;
            }
        } catch (Exception e) {
            System.err.println("使用redisUtils获取用户智能体列表也失败: " + e.getMessage());
        }

        // 缓存未命中，查询数据库
        List<AgentDTO> agents = agentService.getUserAgents(userId);
        // 将查询结果存入缓存
        if (agents != null && !agents.isEmpty()) {
            redisUtils.set(cacheKey, agents);
        }
        return agents;
    }

    /**
     * 从缓存获取用户设备智能体关联，缓存未命中时查询数据库并存入缓存
     */
    private AgentEntity getAgentByUserIdAndDeviceIdFromCache(Long userId, String deviceId) {
        String cacheKey = RedisKeys.getAgentByUserDeviceKey(userId, deviceId);
        AgentEntity cachedAgent = (AgentEntity) redisUtils.get(cacheKey);
        if (cachedAgent != null) {
            return cachedAgent;
        }
        // 缓存未命中，查询数据库
        AgentEntity agent = agentService.getAgentByUserIdAndDeviceId(userId, deviceId);
        // 将查询结果存入缓存（即使为null也缓存，避免缓存穿透）
        if (agent != null) {
            redisUtils.set(cacheKey, agent);
        }
        return agent;
    }

    /**
     * 从缓存获取音色信息，缓存未命中时查询数据库并存入缓存
     */
    private TimbreDetailsVO getTimbreFromCache(String timbreId) {
        if (StringUtils.isBlank(timbreId)) {
            return null;
        }
        String cacheKey = RedisKeys.getTimbreDetailsKey(timbreId);
        TimbreDetailsVO cachedTimbre = (TimbreDetailsVO) redisUtils.get(cacheKey);
        if (cachedTimbre != null) {
            return cachedTimbre;
        }
        // 缓存未命中，查询数据库
        TimbreDetailsVO timbre = timbreService.get(timbreId);
        // 注意：timbreService.get()方法内部已经处理了缓存存储，这里不需要重复存储
        return timbre;
    }

    /**
     * 从缓存获取系统参数，缓存未命中时查询数据库并存入缓存
     */
    private String getSysParamFromCache(String paramCode) {
        String cacheKey = RedisKeys.getSysParamKey(paramCode);
        String cachedValue = (String) redisUtils.get(cacheKey);
        if (cachedValue != null) {
            return cachedValue;
        }
        // 缓存未命中，查询数据库
        // 注意：sysParamsService.getValue(paramCode, true)方法内部已经处理了缓存存储
        return sysParamsService.getValue(paramCode, true);
    }

    /**
     * 从缓存获取智能体插件参数，缓存未命中时查询数据库并存入缓存
     */
    private List<AgentPluginMapping> getAgentPluginMappingsFromCache(String agentId) {
        String cacheKey = RedisKeys.getAgentPluginMappingsKey(agentId);
        @SuppressWarnings("unchecked")
        List<AgentPluginMapping> cachedMappings = (List<AgentPluginMapping>) redisUtils.get(cacheKey);
        if (cachedMappings != null) {
            return cachedMappings;
        }
        // 缓存未命中，查询数据库
        List<AgentPluginMapping> mappings = agentPluginMappingService.agentPluginParamsByAgentId(agentId);
        // 将查询结果存入缓存
        if (mappings != null) {
            redisUtils.set(cacheKey, mappings);
        }
        return mappings;
    }

    /**
     * 从缓存获取智能体声纹信息，缓存未命中时查询数据库并存入缓存
     */
    private List<AgentVoicePrintVO> getAgentVoiceprintsFromCache(String agentId) {
        String cacheKey = RedisKeys.getAgentVoiceprintsKey(agentId);
        @SuppressWarnings("unchecked")
        List<AgentVoicePrintVO> cachedVoiceprints = (List<AgentVoicePrintVO>) redisUtils.get(cacheKey);
        if (cachedVoiceprints != null) {
            return cachedVoiceprints;
        }
        // 缓存未命中，查询数据库
        List<AgentVoicePrintVO> voiceprints = getVoiceprintsByAgentId(agentId);
        // 将查询结果存入缓存
        if (voiceprints != null) {
            redisUtils.set(cacheKey, voiceprints);
        }
        return voiceprints;
    }

    /**
     * 从缓存获取模型配置，缓存未命中时查询数据库并存入缓存
     */
    private ModelConfigEntity getModelConfigFromCache(String modelId) {
        if (StringUtils.isBlank(modelId)) {
            return null;
        }
        String cacheKey = RedisKeys.getModelConfigById(modelId);
        ModelConfigEntity cachedModel = (ModelConfigEntity) redisUtils.get(cacheKey);
        if (cachedModel != null) {
            return cachedModel;
        }
        // 缓存未命中，查询数据库
        ModelConfigEntity model = modelConfigService.getModelById(modelId, false);
        // 将查询结果存入缓存
        if (model != null) {
            redisUtils.set(cacheKey, model);
        }
        return model;
    }

    /**
     * 批量从缓存获取系统参数
     */
    private Map<String, String> getSysParamsBatchFromCache(List<String> paramCodes) {
        Map<String, String> result = new HashMap<>();
        List<String> missedCodes = new ArrayList<>();

        // 先尝试从缓存获取
        for (String paramCode : paramCodes) {
            String cacheKey = RedisKeys.getSysParamKey(paramCode);
            String cachedValue = (String) redisUtils.get(cacheKey);
            if (cachedValue != null) {
                result.put(paramCode, cachedValue);
            } else {
                missedCodes.add(paramCode);
            }
        }

        // 对于缓存未命中的参数，从数据库获取
        for (String missedCode : missedCodes) {
            String value = sysParamsService.getValue(missedCode, true);
            result.put(missedCode, value);
        }

        return result;
    }

    // ==================== 类型转换辅助方法 ====================

    /**
     * 安全地将对象转换为字符串
     */
    private String convertToString(Object obj) {
        if (obj == null) {
            return null;
        }
        return obj.toString();
    }

    /**
     * 安全地将对象转换为long
     */
    private long convertToLong(Object obj) {
        if (obj == null) {
            return 0L;
        }
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        try {
            return Long.parseLong(obj.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    /**
     * 安全地将对象转换为int
     */
    private int convertToInt(Object obj) {
        if (obj == null) {
            return 0;
        }
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        try {
            return Integer.parseInt(obj.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 安全地将对象转换为boolean
     */
    private boolean convertToBoolean(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        return Boolean.parseBoolean(obj.toString());
    }

    /**
     * 手动解析设备信息JSON字符串（备用方案）
     */
    private ComputerDeviceInfo parseDeviceInfoManually(String jsonStr) {
        try {
            ComputerDeviceInfo deviceInfo = new ComputerDeviceInfo();

            // 简单的字符串解析，提取关键字段
            if (jsonStr.contains("\"ID\":")) {
                String idStr = extractJsonValue(jsonStr, "\"ID\":");
                if (idStr != null) {
                    deviceInfo.setId(Long.parseLong(idStr.replaceAll("[^0-9]", "")));
                }
            }

            deviceInfo.setDeviceId(extractJsonStringValue(jsonStr, "\"deviceId\":"));
            deviceInfo.setDeviceName(extractJsonStringValue(jsonStr, "\"deviceName\":"));
            deviceInfo.setOsName(extractJsonStringValue(jsonStr, "\"osName\":"));
            deviceInfo.setOsVersion(extractJsonStringValue(jsonStr, "\"osVersion\":"));
            deviceInfo.setUserHomeDir(extractJsonStringValue(jsonStr, "\"userHomeDir\":"));
            deviceInfo.setIpAddress(extractJsonStringValue(jsonStr, "\"ipAddress\":"));

            String userIdStr = extractJsonValue(jsonStr, "\"userId\":");
            if (userIdStr != null && !userIdStr.isEmpty()) {
                try {
                    String numStr = userIdStr.replaceAll("[^0-9]", "");
                    if (!numStr.isEmpty()) {
                        deviceInfo.setUserId(Long.parseLong(numStr));
                    }
                } catch (NumberFormatException e) {
                    System.err.println("解析userId失败: " + userIdStr);
                }
            }

            String statusStr = extractJsonValue(jsonStr, "\"status\":");
            if (statusStr != null && !statusStr.isEmpty()) {
                try {
                    String numStr = statusStr.replaceAll("[^0-9]", "");
                    if (!numStr.isEmpty()) {
                        deviceInfo.setStatus(Integer.parseInt(numStr));
                    }
                } catch (NumberFormatException e) {
                    System.err.println("解析status失败: " + statusStr);
                }
            }

            return deviceInfo;
        } catch (Exception e) {
            System.err.println("手动解析JSON失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从JSON字符串中提取指定字段的值
     */
    private String extractJsonValue(String jsonStr, String fieldName) {
        try {
            int startIndex = jsonStr.indexOf(fieldName);
            if (startIndex == -1) return null;

            startIndex += fieldName.length();

            // 跳过空格
            while (startIndex < jsonStr.length() && Character.isWhitespace(jsonStr.charAt(startIndex))) {
                startIndex++;
            }

            int endIndex;
            char firstChar = jsonStr.charAt(startIndex);

            if (firstChar == '"') {
                // 字符串值：查找结束引号
                startIndex++; // 跳过开始引号
                endIndex = startIndex;
                while (endIndex < jsonStr.length()) {
                    if (jsonStr.charAt(endIndex) == '"' && jsonStr.charAt(endIndex - 1) != '\\') {
                        break;
                    }
                    endIndex++;
                }
                return jsonStr.substring(startIndex, endIndex);
            } else if (firstChar == '[') {
                // 数组值：查找匹配的结束括号
                int bracketCount = 1;
                endIndex = startIndex + 1;
                while (endIndex < jsonStr.length() && bracketCount > 0) {
                    char c = jsonStr.charAt(endIndex);
                    if (c == '[') bracketCount++;
                    else if (c == ']') bracketCount--;
                    endIndex++;
                }
                return jsonStr.substring(startIndex, endIndex);
            } else {
                // 数值或布尔值：查找逗号或结束括号
                endIndex = jsonStr.indexOf(",", startIndex);
                if (endIndex == -1) {
                    endIndex = jsonStr.indexOf("}", startIndex);
                }
                if (endIndex == -1) return null;
                return jsonStr.substring(startIndex, endIndex).trim();
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JSON字符串中提取字符串类型字段的值
     */
    private String extractJsonStringValue(String jsonStr, String fieldName) {
        String value = extractJsonValue(jsonStr, fieldName);
        if (value != null) {
            // 如果是数组格式，直接返回（保持原始格式）
            if (value.startsWith("[") && value.endsWith("]")) {
                return value;
            }
            // 如果是普通字符串，移除引号
            if ("null".equals(value) || value.isEmpty()) {
                return null;
            }
        }
        return value;
    }

    // ==================== JSON 安全解析辅助方法 ====================

    /**
     * 安全地从JSONObject获取Long值，避免null指针异常
     */
    private long getLongSafely(JSONObject jsonObj, String key) {
        Long value = jsonObj.getLong(key);
        return value != null ? value : 0L;
    }

    /**
     * 安全地从JSONObject获取Integer值，避免null指针异常
     */
    private int getIntegerSafely(JSONObject jsonObj, String key) {
        Integer value = jsonObj.getInteger(key);
        return value != null ? value : 0;
    }

    /**
     * 安全地从JSONObject获取Boolean值，避免null指针异常
     */
    private boolean getBooleanSafely(JSONObject jsonObj, String key) {
        Boolean value = jsonObj.getBoolean(key);
        return value != null ? value : false;
    }

    /**
     * 从Redis获取额外的配置信息并添加到结果中
     * 
     * @param result 结果Map
     */
    private void addExtraConfigFromRedis(Map<String, Object> result) {
        try {
            // 1. 获取 mcp-tools 字符串
            String mcpTools = stringRedisTemplate.opsForValue().get("mcp-tools");
            if (StringUtils.isNotBlank(mcpTools)) {
                result.put("mcp_tools", mcpTools);
            }

            // 2. 获取 mcp-tools-grouped JSON字符串并转换成对象
            String mcpToolsGroupedJson = stringRedisTemplate.opsForValue().get("mcp-tools-grouped");
            if (StringUtils.isNotBlank(mcpToolsGroupedJson)) {
                try {
                    Object mcpToolsGrouped = JSONObject.parseObject(mcpToolsGroupedJson, Object.class);
                    result.put("mcp_tools_grouped", mcpToolsGrouped);
                } catch (Exception e) {
                    System.err.println("解析 mcp-tools-grouped JSON 失败: " + e.getMessage());
                }
            }

            // 3. 获取用户联系人信息
            if (result.containsKey("userId") && result.get("userId") != null) {
                String userContactsKey = "user:contacts:" + result.get("userId");
                String userContactsJson = stringRedisTemplate.opsForValue().get(userContactsKey);
                if (StringUtils.isNotBlank(userContactsJson)) {
                    try {
                        // 解析联系人JSON并格式化为标准格式
                        Object userContacts = JSONObject.parseObject(userContactsJson, Object.class);
                        
                        // 如果联系人数据是Java ArrayList格式，提取实际联系人列表
                        if (userContacts instanceof List && ((List<?>) userContacts).size() == 2) {
                            List<?> contactsList = (List<?>) userContacts;
                            if ("java.util.ArrayList".equals(contactsList.get(0)) && contactsList.get(1) instanceof List) {
                                // 提取实际联系人列表
                                List<?> actualContacts = (List<?>) contactsList.get(1);
                                result.put("user_contacts", actualContacts);
                            } else {
                                result.put("user_contacts", userContacts);
                            }
                        } else {
                            result.put("user_contacts", userContacts);
                        }
                    } catch (Exception e) {
                        System.err.println("解析用户联系人 JSON 失败: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("从Redis获取额外配置信息失败: " + e.getMessage());
        }
    }
}
