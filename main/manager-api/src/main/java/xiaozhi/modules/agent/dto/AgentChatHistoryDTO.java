package xiaozhi.modules.agent.dto;

import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体聊天记录DTO
 */
@Data
@Schema(description = "智能体聊天记录")
public class AgentChatHistoryDTO {
    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "消息类型: 1-用户, 2-智能体, 3-工具调用记录")
    private Byte chatType;

    @Schema(description = "聊天内容")
    private String content;

    @Schema(description = "音频ID")
    private String audioId;

    @Schema(description = "MAC地址")
    private String macAddress;

    // 以下字段仅当 chatType = 3 (工具调用记录) 时有值
    @Schema(description = "工具请求参数")
    private String requestParams;

    @Schema(description = "工具响应结果")
    private String responseResult;

    @Schema(description = "MCP名称")
    private String mcpName;

    @Schema(description = "工具名称")
    private String toolName;

    @Schema(description = "是否执行错误")
    private Boolean isError;

    // 以下字段为通用字段，所有类型的聊天记录都可能有值
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "积分")
    private Integer points;

    @Schema(description = "本次聊天消耗的大模型tokens")
    private Integer llmTokens;
}