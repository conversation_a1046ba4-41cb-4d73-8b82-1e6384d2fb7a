-- 为 ai_agent_chat_history 表添加工具调用记录相关字段
-- chat_type = 3 为工具调用记录

-- 添加工具调用专用字段
ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `request_params` TEXT COMMENT '工具请求参数 (仅当chat_type=3时使用)' AFTER `audio_id`;

ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `response_result` TEXT COMMENT '工具响应结果 (仅当chat_type=3时使用)' AFTER `request_params`;

ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `mcp_name` VARCHAR(255) COMMENT 'MCP名称 (仅当chat_type=3时使用)' AFTER `response_result`;

ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `tool_name` VARCHAR(255) COMMENT '工具名称 (仅当chat_type=3时使用)' AFTER `mcp_name`;

ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `is_error` TINYINT(1) COMMENT '是否执行错误 (仅当chat_type=3时使用)' AFTER `tool_name`;

-- 添加通用字段
ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `user_id` BIGINT COMMENT '用户ID' AFTER `is_error`;

ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `points` INT COMMENT '积分' AFTER `user_id`;

ALTER TABLE `ai_agent_chat_history` 
ADD COLUMN `llm_tokens` INT COMMENT '本次聊天消耗的大模型tokens' AFTER `points`;
